/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!./node_modules/next/font/local/target.css?{"path":"node_modules\\geist\\dist\\sans.js","import":"","arguments":[{"src":"./fonts/geist-sans/Geist-Variable.woff2","variable":"--font-geist-sans","weight":"100 900"}],"variableName":"GeistSans"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: 'GeistSans';
src: url(/_next/static/media/028c0d39d2e8f589-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 100 900;
}@font-face {font-family: 'GeistSans Fallback';src: local("Arial");ascent-override: 85.83%;descent-override: 20.53%;line-gap-override: 9.33%;size-adjust: 107.19%
}.__className_fb8f2c {font-family: 'GeistSans', 'GeistSans Fallback'
}.__variable_fb8f2c {--font-geist-sans: 'GeistSans', 'GeistSans Fallback'
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!./node_modules/next/font/local/target.css?{"path":"node_modules\\geist\\dist\\mono.js","import":"","arguments":[{"src":"./fonts/geist-mono/GeistMono-Variable.woff2","variable":"--font-geist-mono","adjustFontFallback":false,"fallback":["ui-monospace","SFMono-Regular","Roboto Mono","Menlo","Monaco","Liberation Mono","DejaVu Sans Mono","Courier New","monospace"],"weight":"100 900"}],"variableName":"GeistMono"} ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: 'GeistMono';
src: url(/_next/static/media/5b01f339abf2f1a5.p.woff2) format('woff2');
font-display: swap;
font-weight: 100 900;
}.__className_f910ec {font-family: 'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace
}.__variable_f910ec {--font-geist-mono: 'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace
}

/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-indigo-50: oklch(96.2% 0.018 272.314);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-widest: 0.1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: 0.125rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --blur-sm: 8px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-border: var(--border);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .\@container\/card-header {
    container-type: inline-size;
    container-name: card-header;
  }
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .invisible {
    visibility: hidden;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-12 {
    top: calc(var(--spacing) * -12);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-\[1px\] {
    top: 1px;
  }
  .top-\[50\%\] {
    top: 50%;
  }
  .top-\[60\%\] {
    top: 60%;
  }
  .top-full {
    top: 100%;
  }
  .-right-4 {
    right: calc(var(--spacing) * -4);
  }
  .-right-12 {
    right: calc(var(--spacing) * -12);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }
  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .-left-12 {
    left: calc(var(--spacing) * -12);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-\[50\%\] {
    left: 50%;
  }
  .isolate {
    isolation: isolate;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[1\] {
    z-index: 1;
  }
  .z-\[100\] {
    z-index: 100;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-start-2 {
    grid-column-start: 2;
  }
  .row-span-2 {
    grid-row: span 2 / span 2;
  }
  .row-start-1 {
    grid-row-start: 1;
  }
  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-0\.5 {
    margin-block: calc(var(--spacing) * 0.5);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-auto {
    margin-top: auto;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-auto {
    margin-left: auto;
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-row {
    display: table-row;
  }
  .field-sizing-content {
    field-sizing: content;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .size-\(--cell-size\) {
    width: var(--cell-size);
    height: var(--cell-size);
  }
  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }
  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }
  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }
  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }
  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }
  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }
  .size-auto {
    width: auto;
    height: auto;
  }
  .size-full {
    width: 100%;
    height: 100%;
  }
  .h-\(--cell-size\) {
    height: var(--cell-size);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-\[1\.15rem\] {
    height: 1.15rem;
  }
  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }
  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }
  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-svh {
    height: 100svh;
  }
  .max-h-\(--radix-context-menu-content-available-height\) {
    max-height: var(--radix-context-menu-content-available-height);
  }
  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }
  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }
  .max-h-\[300px\] {
    max-height: 300px;
  }
  .max-h-screen {
    max-height: 100vh;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }
  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .min-h-svh {
    min-height: 100svh;
  }
  .w-\(--cell-size\) {
    width: var(--cell-size);
  }
  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-\[100px\] {
    width: 100px;
  }
  .w-auto {
    width: auto;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .w-max {
    width: max-content;
  }
  .w-px {
    width: 1px;
  }
  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }
  .max-w-max {
    max-width: max-content;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .min-w-\(--cell-size\) {
    min-width: var(--cell-size);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }
  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }
  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }
  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }
  .min-w-\[8rem\] {
    min-width: 8rem;
  }
  .min-w-\[12rem\] {
    min-width: 12rem;
  }
  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .grow-0 {
    flex-grow: 0;
  }
  .basis-full {
    flex-basis: 100%;
  }
  .caption-bottom {
    caption-side: bottom;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .origin-\(--radix-context-menu-content-transform-origin\) {
    transform-origin: var(--radix-context-menu-content-transform-origin);
  }
  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }
  .origin-\(--radix-hover-card-content-transform-origin\) {
    transform-origin: var(--radix-hover-card-content-transform-origin);
  }
  .origin-\(--radix-menubar-content-transform-origin\) {
    transform-origin: var(--radix-menubar-content-transform-origin);
  }
  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }
  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }
  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * 0.5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .rotate-45 {
    rotate: 45deg;
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-caret-blink {
    animation: caret-blink 1.25s ease-out infinite;
  }
  .animate-in {
    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .cursor-default {
    cursor: default;
  }
  .touch-none {
    touch-action: none;
  }
  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }
  .scroll-py-1 {
    scroll-padding-block: calc(var(--spacing) * 1);
  }
  .list-none {
    list-style-type: none;
  }
  .auto-rows-min {
    grid-auto-rows: min-content;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-\[0_1fr\] {
    grid-template-columns: 0 1fr;
  }
  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-items-start {
    justify-items: start;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * 0.5);
  }
  .self-start {
    align-self: flex-start;
  }
  .justify-self-end {
    justify-self: flex-end;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded-\[2px\] {
    border-radius: 2px;
  }
  .rounded-\[4px\] {
    border-radius: 4px;
  }
  .rounded-\[inherit\] {
    border-radius: inherit;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius);
  }
  .rounded-md {
    border-radius: calc(var(--radius) - 2px);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: calc(var(--radius) - 4px);
  }
  .rounded-xl {
    border-radius: calc(var(--radius) + 4px);
  }
  .rounded-xs {
    border-radius: var(--radius-xs);
  }
  .rounded-l-md {
    border-top-left-radius: calc(var(--radius) - 2px);
    border-bottom-left-radius: calc(var(--radius) - 2px);
  }
  .rounded-tl-sm {
    border-top-left-radius: calc(var(--radius) - 4px);
  }
  .rounded-r-md {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-\(--color-border\) {
    border-color: var(--color-border);
  }
  .border-border\/50 {
    border-color: var(--border);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }
  .border-destructive {
    border-color: var(--destructive);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-800 {
    border-color: var(--color-gray-800);
  }
  .border-input {
    border-color: var(--input);
  }
  .border-primary {
    border-color: var(--primary);
  }
  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .border-l-transparent {
    border-left-color: transparent;
  }
  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }
  .bg-accent {
    background-color: var(--accent);
  }
  .bg-background {
    background-color: var(--background);
  }
  .bg-black\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-border {
    background-color: var(--border);
  }
  .bg-card {
    background-color: var(--card);
  }
  .bg-destructive {
    background-color: var(--destructive);
  }
  .bg-foreground {
    background-color: var(--foreground);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-muted {
    background-color: var(--muted);
  }
  .bg-muted\/50 {
    background-color: var(--muted);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }
  .bg-popover {
    background-color: var(--popover);
  }
  .bg-primary {
    background-color: var(--primary);
  }
  .bg-primary\/20 {
    background-color: var(--primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }
  .bg-secondary {
    background-color: var(--secondary);
  }
  .bg-sidebar {
    background-color: var(--sidebar);
  }
  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/95 {
    background-color: color-mix(in srgb, #fff 95%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-\[url\(\'\/placeholder-7tkdz\.png\'\)\] {
    background-image: url('/placeholder-7tkdz.png');
  }
  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-indigo-50 {
    --tw-gradient-to: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-center {
    background-position: center;
  }
  .fill-current {
    fill: currentcolor;
  }
  .fill-primary {
    fill: var(--primary);
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-\[3px\] {
    padding: 3px;
  }
  .p-px {
    padding: 1px;
  }
  .px-\(--cell-size\) {
    padding-inline: var(--cell-size);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[0\.8rem\] {
    font-size: 0.8rem;
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }
  .text-balance {
    text-wrap: balance;
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-accent-foreground {
    color: var(--accent-foreground);
  }
  .text-blue-100 {
    color: var(--color-blue-100);
  }
  .text-blue-200 {
    color: var(--color-blue-200);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-card-foreground {
    color: var(--card-foreground);
  }
  .text-current {
    color: currentcolor;
  }
  .text-destructive {
    color: var(--destructive);
  }
  .text-destructive-foreground {
    color: var(--destructive-foreground);
  }
  .text-foreground {
    color: var(--foreground);
  }
  .text-foreground\/50 {
    color: var(--foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--foreground) 50%, transparent);
    }
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-muted-foreground {
    color: var(--muted-foreground);
  }
  .text-popover-foreground {
    color: var(--popover-foreground);
  }
  .text-primary {
    color: var(--primary);
  }
  .text-primary-foreground {
    color: var(--primary-foreground);
  }
  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }
  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }
  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-5 {
    opacity: 5%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-90 {
    opacity: 90%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-ring\/50 {
    --tw-ring-color: var(--ring);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }
  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }
  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }
  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[color\,box-shadow\] {
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[left\,right\,width\] {
    transition-property: left,right,width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[margin\,opacity\] {
    transition-property: margin,opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[width\,height\,padding\] {
    transition-property: width,height,padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-none {
    transition-property: none;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }
  .fade-in-0 {
    --tw-enter-opacity: calc(0/100);
    --tw-enter-opacity: 0;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .zoom-in-95 {
    --tw-enter-scale: calc(95*1%);
    --tw-enter-scale: .95;
  }
  .\[--cell-size\:--spacing\(8\)\] {
    --cell-size: calc(var(--spacing) * 8);
  }
  .group-focus-within\/menu-item\:opacity-100 {
    &:is(:where(.group\/menu-item):focus-within *) {
      opacity: 100%;
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-hover\/menu-item\:opacity-100 {
    &:is(:where(.group\/menu-item):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8 {
    &:is(:where(.group\/menu-item):has(*[data-sidebar="menu-action"]) *) {
      padding-right: calc(var(--spacing) * 8);
    }
  }
  .group-data-\[collapsible\=icon\]\:-mt-8 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      margin-top: calc(var(--spacing) * -8);
    }
  }
  .group-data-\[collapsible\=icon\]\:hidden {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      display: none;
    }
  }
  .group-data-\[collapsible\=icon\]\:size-8\! {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--spacing) * 8) !important;
      height: calc(var(--spacing) * 8) !important;
    }
  }
  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\) {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: var(--sidebar-width-icon);
    }
  }
  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\] {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));
    }
  }
  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\] {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);
    }
  }
  .group-data-\[collapsible\=icon\]\:overflow-hidden {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      overflow: hidden;
    }
  }
  .group-data-\[collapsible\=icon\]\:p-0\! {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      padding: calc(var(--spacing) * 0) !important;
    }
  }
  .group-data-\[collapsible\=icon\]\:p-2\! {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      padding: calc(var(--spacing) * 2) !important;
    }
  }
  .group-data-\[collapsible\=icon\]\:opacity-0 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      opacity: 0%;
    }
  }
  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      right: calc(var(--sidebar-width) * -1);
    }
  }
  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      left: calc(var(--sidebar-width) * -1);
    }
  }
  .group-data-\[collapsible\=offcanvas\]\:w-0 {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      width: calc(var(--spacing) * 0);
    }
  }
  .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-data-\[disabled\=true\]\:pointer-events-none {
    &:is(:where(.group)[data-disabled="true"] *) {
      pointer-events: none;
    }
  }
  .group-data-\[disabled\=true\]\:opacity-50 {
    &:is(:where(.group)[data-disabled="true"] *) {
      opacity: 50%;
    }
  }
  .group-data-\[focused\=true\]\/day\:relative {
    &:is(:where(.group\/day)[data-focused="true"] *) {
      position: relative;
    }
  }
  .group-data-\[focused\=true\]\/day\:z-10 {
    &:is(:where(.group\/day)[data-focused="true"] *) {
      z-index: 10;
    }
  }
  .group-data-\[focused\=true\]\/day\:border-ring {
    &:is(:where(.group\/day)[data-focused="true"] *) {
      border-color: var(--ring);
    }
  }
  .group-data-\[focused\=true\]\/day\:ring-\[3px\] {
    &:is(:where(.group\/day)[data-focused="true"] *) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .group-data-\[focused\=true\]\/day\:ring-ring\/50 {
    &:is(:where(.group\/day)[data-focused="true"] *) {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
      }
    }
  }
  .group-data-\[side\=left\]\:-right-4 {
    &:is(:where(.group)[data-side="left"] *) {
      right: calc(var(--spacing) * -4);
    }
  }
  .group-data-\[side\=left\]\:border-r {
    &:is(:where(.group)[data-side="left"] *) {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .group-data-\[side\=right\]\:left-0 {
    &:is(:where(.group)[data-side="right"] *) {
      left: calc(var(--spacing) * 0);
    }
  }
  .group-data-\[side\=right\]\:rotate-180 {
    &:is(:where(.group)[data-side="right"] *) {
      rotate: 180deg;
    }
  }
  .group-data-\[side\=right\]\:border-l {
    &:is(:where(.group)[data-side="right"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .group-data-\[state\=open\]\:rotate-180 {
    &:is(:where(.group)[data-state="open"] *) {
      rotate: 180deg;
    }
  }
  .group-data-\[variant\=floating\]\:rounded-lg {
    &:is(:where(.group)[data-variant="floating"] *) {
      border-radius: var(--radius);
    }
  }
  .group-data-\[variant\=floating\]\:border {
    &:is(:where(.group)[data-variant="floating"] *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-data-\[variant\=floating\]\:border-sidebar-border {
    &:is(:where(.group)[data-variant="floating"] *) {
      border-color: var(--sidebar-border);
    }
  }
  .group-data-\[variant\=floating\]\:shadow-sm {
    &:is(:where(.group)[data-variant="floating"] *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block {
    &:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
      display: block;
    }
  }
  .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:text-center {
    &:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
      text-align: center;
    }
  }
  .group-data-\[vaul-drawer-direction\=top\]\/drawer-content\:text-center {
    &:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="top"] *) {
      text-align: center;
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:top-full {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      top: 100%;
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      margin-top: calc(var(--spacing) * 1.5);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      overflow: hidden;
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      border-radius: calc(var(--radius) - 2px);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:border {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      background-color: var(--popover);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      color: var(--popover-foreground);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:shadow {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      --tw-duration: 200ms;
      transition-duration: 200ms;
    }
  }
  .group-\[\.destructive\]\:border-muted\/40 {
    &:is(:where(.group):is(.destructive) *) {
      border-color: var(--muted);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--muted) 40%, transparent);
      }
    }
  }
  .group-\[\.destructive\]\:text-red-300 {
    &:is(:where(.group):is(.destructive) *) {
      color: var(--color-red-300);
    }
  }
  .peer-hover\/menu-button\:text-sidebar-accent-foreground {
    &:is(:where(.peer\/menu-button):hover ~ *) {
      @media (hover: hover) {
        color: var(--sidebar-accent-foreground);
      }
    }
  }
  .peer-disabled\:cursor-not-allowed {
    &:is(:where(.peer):disabled ~ *) {
      cursor: not-allowed;
    }
  }
  .peer-disabled\:opacity-50 {
    &:is(:where(.peer):disabled ~ *) {
      opacity: 50%;
    }
  }
  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground {
    &:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }
  .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
    &:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
      top: calc(var(--spacing) * 1.5);
    }
  }
  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
    &:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
      top: calc(var(--spacing) * 2.5);
    }
  }
  .peer-data-\[size\=sm\]\/menu-button\:top-1 {
    &:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
      top: calc(var(--spacing) * 1);
    }
  }
  .selection\:bg-primary {
    & *::selection {
      background-color: var(--primary);
    }
    &::selection {
      background-color: var(--primary);
    }
  }
  .selection\:text-primary-foreground {
    & *::selection {
      color: var(--primary-foreground);
    }
    &::selection {
      color: var(--primary-foreground);
    }
  }
  .file\:inline-flex {
    &::file-selector-button {
      display: inline-flex;
    }
  }
  .file\:h-7 {
    &::file-selector-button {
      height: calc(var(--spacing) * 7);
    }
  }
  .file\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\:bg-transparent {
    &::file-selector-button {
      background-color: transparent;
    }
  }
  .file\:text-sm {
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .file\:font-medium {
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .file\:text-foreground {
    &::file-selector-button {
      color: var(--foreground);
    }
  }
  .placeholder\:text-muted-foreground {
    &::placeholder {
      color: var(--muted-foreground);
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:-inset-2 {
    &::after {
      content: var(--tw-content);
      inset: calc(var(--spacing) * -2);
    }
  }
  .after\:inset-y-0 {
    &::after {
      content: var(--tw-content);
      inset-block: calc(var(--spacing) * 0);
    }
  }
  .after\:left-1\/2 {
    &::after {
      content: var(--tw-content);
      left: calc(1/2 * 100%);
    }
  }
  .after\:w-1 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 1);
    }
  }
  .after\:w-\[2px\] {
    &::after {
      content: var(--tw-content);
      width: 2px;
    }
  }
  .after\:-translate-x-1\/2 {
    &::after {
      content: var(--tw-content);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-data-\[collapsible\=offcanvas\]\:after\:left-full {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      &::after {
        content: var(--tw-content);
        left: 100%;
      }
    }
  }
  .first\:rounded-l-md {
    &:first-child {
      border-top-left-radius: calc(var(--radius) - 2px);
      border-bottom-left-radius: calc(var(--radius) - 2px);
    }
  }
  .first\:border-l {
    &:first-child {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .last\:rounded-r-md {
    &:last-child {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .last\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .hover\:-translate-y-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:bg-accent {
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
  }
  .hover\:bg-destructive\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-muted {
    &:hover {
      @media (hover: hover) {
        background-color: var(--muted);
      }
    }
  }
  .hover\:bg-muted\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--muted);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--muted) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-primary\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--primary) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-secondary {
    &:hover {
      @media (hover: hover) {
        background-color: var(--secondary);
      }
    }
  }
  .hover\:bg-secondary\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--secondary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
        }
      }
    }
  }
  .hover\:bg-sidebar-accent {
    &:hover {
      @media (hover: hover) {
        background-color: var(--sidebar-accent);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:text-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--foreground);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-muted-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--muted-foreground);
      }
    }
  }
  .hover\:text-sidebar-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: var(--sidebar-accent-foreground);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:opacity-100 {
    &:hover {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\] {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:ring-4 {
    &:hover {
      @media (hover: hover) {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar {
    &:hover {
      @media (hover: hover) {
        &:is(:where(.group)[data-collapsible="offcanvas"] *) {
          background-color: var(--sidebar);
        }
      }
    }
  }
  .group-\[\.destructive\]\:hover\:border-destructive\/30 {
    &:is(:where(.group):is(.destructive) *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--destructive);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--destructive) 30%, transparent);
          }
        }
      }
    }
  }
  .group-\[\.destructive\]\:hover\:bg-destructive {
    &:is(:where(.group):is(.destructive) *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--destructive);
        }
      }
    }
  }
  .group-\[\.destructive\]\:hover\:text-destructive-foreground {
    &:is(:where(.group):is(.destructive) *) {
      &:hover {
        @media (hover: hover) {
          color: var(--destructive-foreground);
        }
      }
    }
  }
  .group-\[\.destructive\]\:hover\:text-red-50 {
    &:is(:where(.group):is(.destructive) *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-red-50);
        }
      }
    }
  }
  .hover\:after\:bg-sidebar-border {
    &:hover {
      @media (hover: hover) {
        &::after {
          content: var(--tw-content);
          background-color: var(--sidebar-border);
        }
      }
    }
  }
  .focus\:z-10 {
    &:focus {
      z-index: 10;
    }
  }
  .focus\:bg-accent {
    &:focus {
      background-color: var(--accent);
    }
  }
  .focus\:text-accent-foreground {
    &:focus {
      color: var(--accent-foreground);
    }
  }
  .focus\:opacity-100 {
    &:focus {
      opacity: 100%;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-ring {
    &:focus {
      --tw-ring-color: var(--ring);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-hidden {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .group-\[\.destructive\]\:focus\:ring-destructive {
    &:is(:where(.group):is(.destructive) *) {
      &:focus {
        --tw-ring-color: var(--destructive);
      }
    }
  }
  .group-\[\.destructive\]\:focus\:ring-red-400 {
    &:is(:where(.group):is(.destructive) *) {
      &:focus {
        --tw-ring-color: var(--color-red-400);
      }
    }
  }
  .group-\[\.destructive\]\:focus\:ring-offset-red-600 {
    &:is(:where(.group):is(.destructive) *) {
      &:focus {
        --tw-ring-offset-color: var(--color-red-600);
      }
    }
  }
  .focus-visible\:z-10 {
    &:focus-visible {
      z-index: 10;
    }
  }
  .focus-visible\:border-ring {
    &:focus-visible {
      border-color: var(--ring);
    }
  }
  .focus-visible\:ring-1 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-4 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-\[3px\] {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-destructive\/20 {
    &:focus-visible {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
  .focus-visible\:ring-ring {
    &:focus-visible {
      --tw-ring-color: var(--ring);
    }
  }
  .focus-visible\:ring-ring\/50 {
    &:focus-visible {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
      }
    }
  }
  .focus-visible\:ring-offset-1 {
    &:focus-visible {
      --tw-ring-offset-width: 1px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\:outline-hidden {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .focus-visible\:outline-1 {
    &:focus-visible {
      outline-style: var(--tw-outline-style);
      outline-width: 1px;
    }
  }
  .focus-visible\:outline-ring {
    &:focus-visible {
      outline-color: var(--ring);
    }
  }
  .active\:bg-sidebar-accent {
    &:active {
      background-color: var(--sidebar-accent);
    }
  }
  .active\:text-sidebar-accent-foreground {
    &:active {
      color: var(--sidebar-accent-foreground);
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .in-data-\[side\=left\]\:cursor-w-resize {
    :where(*[data-side="left"]) & {
      cursor: w-resize;
    }
  }
  .in-data-\[side\=right\]\:cursor-e-resize {
    :where(*[data-side="right"]) & {
      cursor: e-resize;
    }
  }
  .has-focus\:border-ring {
    &:has(*:focus) {
      border-color: var(--ring);
    }
  }
  .has-focus\:ring-\[3px\] {
    &:has(*:focus) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .has-focus\:ring-ring\/50 {
    &:has(*:focus) {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
      }
    }
  }
  .has-disabled\:opacity-50 {
    &:has(*:disabled) {
      opacity: 50%;
    }
  }
  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\] {
    &:has(*[data-slot="card-action"]) {
      grid-template-columns: 1fr auto;
    }
  }
  .has-data-\[variant\=inset\]\:bg-sidebar {
    &:has(*[data-variant="inset"]) {
      background-color: var(--sidebar);
    }
  }
  .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\] {
    &:has(>svg) {
      grid-template-columns: calc(var(--spacing) * 4) 1fr;
    }
  }
  .has-\[\>svg\]\:gap-x-3 {
    &:has(>svg) {
      column-gap: calc(var(--spacing) * 3);
    }
  }
  .has-\[\>svg\]\:px-2\.5 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 2.5);
    }
  }
  .has-\[\>svg\]\:px-3 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .has-\[\>svg\]\:px-4 {
    &:has(>svg) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .aria-disabled\:pointer-events-none {
    &[aria-disabled="true"] {
      pointer-events: none;
    }
  }
  .aria-disabled\:opacity-50 {
    &[aria-disabled="true"] {
      opacity: 50%;
    }
  }
  .aria-invalid\:border-destructive {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
  }
  .aria-invalid\:ring-destructive\/20 {
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
  .aria-selected\:text-muted-foreground {
    &[aria-selected="true"] {
      color: var(--muted-foreground);
    }
  }
  .data-\[active\=true\]\:z-10 {
    &[data-active="true"] {
      z-index: 10;
    }
  }
  .data-\[active\=true\]\:border-ring {
    &[data-active="true"] {
      border-color: var(--ring);
    }
  }
  .data-\[active\=true\]\:bg-accent\/50 {
    &[data-active="true"] {
      background-color: var(--accent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }
  .data-\[active\=true\]\:bg-sidebar-accent {
    &[data-active="true"] {
      background-color: var(--sidebar-accent);
    }
  }
  .data-\[active\=true\]\:font-medium {
    &[data-active="true"] {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .data-\[active\=true\]\:text-accent-foreground {
    &[data-active="true"] {
      color: var(--accent-foreground);
    }
  }
  .data-\[active\=true\]\:text-sidebar-accent-foreground {
    &[data-active="true"] {
      color: var(--sidebar-accent-foreground);
    }
  }
  .data-\[active\=true\]\:ring-\[3px\] {
    &[data-active="true"] {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .data-\[active\=true\]\:ring-ring\/50 {
    &[data-active="true"] {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
      }
    }
  }
  .data-\[active\=true\]\:hover\:bg-accent {
    &[data-active="true"] {
      &:hover {
        @media (hover: hover) {
          background-color: var(--accent);
        }
      }
    }
  }
  .data-\[active\=true\]\:focus\:bg-accent {
    &[data-active="true"] {
      &:focus {
        background-color: var(--accent);
      }
    }
  }
  .data-\[active\=true\]\:aria-invalid\:border-destructive {
    &[data-active="true"] {
      &[aria-invalid="true"] {
        border-color: var(--destructive);
      }
    }
  }
  .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20 {
    &[data-active="true"] {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
      }
    }
  }
  .data-\[disabled\]\:pointer-events-none {
    &[data-disabled] {
      pointer-events: none;
    }
  }
  .data-\[disabled\]\:opacity-50 {
    &[data-disabled] {
      opacity: 50%;
    }
  }
  .data-\[disabled\=true\]\:pointer-events-none {
    &[data-disabled="true"] {
      pointer-events: none;
    }
  }
  .data-\[disabled\=true\]\:opacity-50 {
    &[data-disabled="true"] {
      opacity: 50%;
    }
  }
  .data-\[error\=true\]\:text-destructive {
    &[data-error="true"] {
      color: var(--destructive);
    }
  }
  .data-\[inset\]\:pl-8 {
    &[data-inset] {
      padding-left: calc(var(--spacing) * 8);
    }
  }
  .data-\[motion\=from-end\]\:slide-in-from-right-52 {
    &[data-motion="from-end"] {
      --tw-enter-translate-x: calc(52*var(--spacing));
    }
  }
  .data-\[motion\=from-start\]\:slide-in-from-left-52 {
    &[data-motion="from-start"] {
      --tw-enter-translate-x: calc(52*var(--spacing)*-1);
    }
  }
  .data-\[motion\=to-end\]\:slide-out-to-right-52 {
    &[data-motion="to-end"] {
      --tw-exit-translate-x: calc(52*var(--spacing));
    }
  }
  .data-\[motion\=to-start\]\:slide-out-to-left-52 {
    &[data-motion="to-start"] {
      --tw-exit-translate-x: calc(52*var(--spacing)*-1);
    }
  }
  .data-\[motion\^\=from-\]\:animate-in {
    &[data-motion^="from-"] {
      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[motion\^\=from-\]\:fade-in {
    &[data-motion^="from-"] {
      --tw-enter-opacity: 0;
    }
  }
  .data-\[motion\^\=to-\]\:animate-out {
    &[data-motion^="to-"] {
      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[motion\^\=to-\]\:fade-out {
    &[data-motion^="to-"] {
      --tw-exit-opacity: 0;
    }
  }
  .data-\[orientation\=horizontal\]\:h-1\.5 {
    &[data-orientation="horizontal"] {
      height: calc(var(--spacing) * 1.5);
    }
  }
  .data-\[orientation\=horizontal\]\:h-full {
    &[data-orientation="horizontal"] {
      height: 100%;
    }
  }
  .data-\[orientation\=horizontal\]\:h-px {
    &[data-orientation="horizontal"] {
      height: 1px;
    }
  }
  .data-\[orientation\=horizontal\]\:w-full {
    &[data-orientation="horizontal"] {
      width: 100%;
    }
  }
  .data-\[orientation\=vertical\]\:h-full {
    &[data-orientation="vertical"] {
      height: 100%;
    }
  }
  .data-\[orientation\=vertical\]\:min-h-44 {
    &[data-orientation="vertical"] {
      min-height: calc(var(--spacing) * 44);
    }
  }
  .data-\[orientation\=vertical\]\:w-1\.5 {
    &[data-orientation="vertical"] {
      width: calc(var(--spacing) * 1.5);
    }
  }
  .data-\[orientation\=vertical\]\:w-auto {
    &[data-orientation="vertical"] {
      width: auto;
    }
  }
  .data-\[orientation\=vertical\]\:w-full {
    &[data-orientation="vertical"] {
      width: 100%;
    }
  }
  .data-\[orientation\=vertical\]\:w-px {
    &[data-orientation="vertical"] {
      width: 1px;
    }
  }
  .data-\[orientation\=vertical\]\:flex-col {
    &[data-orientation="vertical"] {
      flex-direction: column;
    }
  }
  .data-\[panel-group-direction\=vertical\]\:h-px {
    &[data-panel-group-direction="vertical"] {
      height: 1px;
    }
  }
  .data-\[panel-group-direction\=vertical\]\:w-full {
    &[data-panel-group-direction="vertical"] {
      width: 100%;
    }
  }
  .data-\[panel-group-direction\=vertical\]\:flex-col {
    &[data-panel-group-direction="vertical"] {
      flex-direction: column;
    }
  }
  .data-\[panel-group-direction\=vertical\]\:after\:left-0 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        left: calc(var(--spacing) * 0);
      }
    }
  }
  .data-\[panel-group-direction\=vertical\]\:after\:h-1 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        height: calc(var(--spacing) * 1);
      }
    }
  }
  .data-\[panel-group-direction\=vertical\]\:after\:w-full {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        width: 100%;
      }
    }
  }
  .data-\[panel-group-direction\=vertical\]\:after\:translate-x-0 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        --tw-translate-y: calc(calc(1/2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .data-\[placeholder\]\:text-muted-foreground {
    &[data-placeholder] {
      color: var(--muted-foreground);
    }
  }
  .data-\[range-end\=true\]\:rounded-md {
    &[data-range-end="true"] {
      border-radius: calc(var(--radius) - 2px);
    }
  }
  .data-\[range-end\=true\]\:rounded-r-md {
    &[data-range-end="true"] {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .data-\[range-end\=true\]\:bg-primary {
    &[data-range-end="true"] {
      background-color: var(--primary);
    }
  }
  .data-\[range-end\=true\]\:text-primary-foreground {
    &[data-range-end="true"] {
      color: var(--primary-foreground);
    }
  }
  .data-\[range-middle\=true\]\:rounded-none {
    &[data-range-middle="true"] {
      border-radius: 0;
    }
  }
  .data-\[range-middle\=true\]\:bg-accent {
    &[data-range-middle="true"] {
      background-color: var(--accent);
    }
  }
  .data-\[range-middle\=true\]\:text-accent-foreground {
    &[data-range-middle="true"] {
      color: var(--accent-foreground);
    }
  }
  .data-\[range-start\=true\]\:rounded-md {
    &[data-range-start="true"] {
      border-radius: calc(var(--radius) - 2px);
    }
  }
  .data-\[range-start\=true\]\:rounded-l-md {
    &[data-range-start="true"] {
      border-top-left-radius: calc(var(--radius) - 2px);
      border-bottom-left-radius: calc(var(--radius) - 2px);
    }
  }
  .data-\[range-start\=true\]\:bg-primary {
    &[data-range-start="true"] {
      background-color: var(--primary);
    }
  }
  .data-\[range-start\=true\]\:text-primary-foreground {
    &[data-range-start="true"] {
      color: var(--primary-foreground);
    }
  }
  .data-\[selected-single\=true\]\:bg-primary {
    &[data-selected-single="true"] {
      background-color: var(--primary);
    }
  }
  .data-\[selected-single\=true\]\:text-primary-foreground {
    &[data-selected-single="true"] {
      color: var(--primary-foreground);
    }
  }
  .data-\[selected\=true\]\:rounded-none {
    &[data-selected="true"] {
      border-radius: 0;
    }
  }
  .data-\[selected\=true\]\:bg-accent {
    &[data-selected="true"] {
      background-color: var(--accent);
    }
  }
  .data-\[selected\=true\]\:text-accent-foreground {
    &[data-selected="true"] {
      color: var(--accent-foreground);
    }
  }
  .data-\[side\=bottom\]\:translate-y-1 {
    &[data-side="bottom"] {
      --tw-translate-y: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[side\=bottom\]\:slide-in-from-top-2 {
    &[data-side="bottom"] {
      --tw-enter-translate-y: calc(2*var(--spacing)*-1);
    }
  }
  .data-\[side\=left\]\:-translate-x-1 {
    &[data-side="left"] {
      --tw-translate-x: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[side\=left\]\:slide-in-from-right-2 {
    &[data-side="left"] {
      --tw-enter-translate-x: calc(2*var(--spacing));
    }
  }
  .data-\[side\=right\]\:translate-x-1 {
    &[data-side="right"] {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[side\=right\]\:slide-in-from-left-2 {
    &[data-side="right"] {
      --tw-enter-translate-x: calc(2*var(--spacing)*-1);
    }
  }
  .data-\[side\=top\]\:-translate-y-1 {
    &[data-side="top"] {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[side\=top\]\:slide-in-from-bottom-2 {
    &[data-side="top"] {
      --tw-enter-translate-y: calc(2*var(--spacing));
    }
  }
  .data-\[size\=default\]\:h-9 {
    &[data-size="default"] {
      height: calc(var(--spacing) * 9);
    }
  }
  .data-\[size\=sm\]\:h-8 {
    &[data-size="sm"] {
      height: calc(var(--spacing) * 8);
    }
  }
  .\*\:data-\[slot\=alert-description\]\:text-destructive\/90 {
    :is(& > *) {
      &[data-slot="alert-description"] {
        color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--destructive) 90%, transparent);
        }
      }
    }
  }
  .\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 {
    :is(& *) {
      &[data-slot="command-input-wrapper"] {
        height: calc(var(--spacing) * 12);
      }
    }
  }
  .\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 {
    :is(& *) {
      &[data-slot="navigation-menu-link"] {
        &:focus {
          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
        }
      }
    }
  }
  .\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none {
    :is(& *) {
      &[data-slot="navigation-menu-link"] {
        &:focus {
          --tw-outline-style: none;
          outline-style: none;
        }
      }
    }
  }
  .\*\:data-\[slot\=select-value\]\:line-clamp-1 {
    :is(& > *) {
      &[data-slot="select-value"] {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
    }
  }
  .\*\:data-\[slot\=select-value\]\:flex {
    :is(& > *) {
      &[data-slot="select-value"] {
        display: flex;
      }
    }
  }
  .\*\:data-\[slot\=select-value\]\:items-center {
    :is(& > *) {
      &[data-slot="select-value"] {
        align-items: center;
      }
    }
  }
  .\*\:data-\[slot\=select-value\]\:gap-2 {
    :is(& > *) {
      &[data-slot="select-value"] {
        gap: calc(var(--spacing) * 2);
      }
    }
  }
  .data-\[state\=active\]\:bg-background {
    &[data-state="active"] {
      background-color: var(--background);
    }
  }
  .data-\[state\=active\]\:shadow-sm {
    &[data-state="active"] {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\] {
    &[data-state="checked"] {
      --tw-translate-x: calc(100% - 2px);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[state\=checked\]\:border-primary {
    &[data-state="checked"] {
      border-color: var(--primary);
    }
  }
  .data-\[state\=checked\]\:bg-primary {
    &[data-state="checked"] {
      background-color: var(--primary);
    }
  }
  .data-\[state\=checked\]\:text-primary-foreground {
    &[data-state="checked"] {
      color: var(--primary-foreground);
    }
  }
  .data-\[state\=closed\]\:animate-accordion-up {
    &[data-state="closed"] {
      animation: accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;
    }
  }
  .data-\[state\=closed\]\:animate-out {
    &[data-state="closed"] {
      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[state\=closed\]\:duration-300 {
    &[data-state="closed"] {
      --tw-duration: 300ms;
      transition-duration: 300ms;
    }
  }
  .data-\[state\=closed\]\:fade-out-0 {
    &[data-state="closed"] {
      --tw-exit-opacity: calc(0/100);
      --tw-exit-opacity: 0;
    }
  }
  .data-\[state\=closed\]\:fade-out-80 {
    &[data-state="closed"] {
      --tw-exit-opacity: calc(80/100);
      --tw-exit-opacity: .8;
    }
  }
  .data-\[state\=closed\]\:zoom-out-95 {
    &[data-state="closed"] {
      --tw-exit-scale: calc(95*1%);
      --tw-exit-scale: .95;
    }
  }
  .data-\[state\=closed\]\:slide-out-to-bottom {
    &[data-state="closed"] {
      --tw-exit-translate-y: 100%;
    }
  }
  .data-\[state\=closed\]\:slide-out-to-left {
    &[data-state="closed"] {
      --tw-exit-translate-x: -100%;
    }
  }
  .data-\[state\=closed\]\:slide-out-to-right {
    &[data-state="closed"] {
      --tw-exit-translate-x: 100%;
    }
  }
  .data-\[state\=closed\]\:slide-out-to-right-full {
    &[data-state="closed"] {
      --tw-exit-translate-x: calc(1*100%);
    }
  }
  .data-\[state\=closed\]\:slide-out-to-top {
    &[data-state="closed"] {
      --tw-exit-translate-y: -100%;
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="closed"] {
        animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
      }
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="closed"] {
        --tw-exit-opacity: calc(0/100);
        --tw-exit-opacity: 0;
      }
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="closed"] {
        --tw-exit-scale: calc(95*1%);
        --tw-exit-scale: .95;
      }
    }
  }
  .data-\[state\=hidden\]\:animate-out {
    &[data-state="hidden"] {
      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[state\=hidden\]\:fade-out {
    &[data-state="hidden"] {
      --tw-exit-opacity: 0;
    }
  }
  .data-\[state\=on\]\:bg-accent {
    &[data-state="on"] {
      background-color: var(--accent);
    }
  }
  .data-\[state\=on\]\:text-accent-foreground {
    &[data-state="on"] {
      color: var(--accent-foreground);
    }
  }
  .data-\[state\=open\]\:animate-accordion-down {
    &[data-state="open"] {
      animation: accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))ease-out;
    }
  }
  .data-\[state\=open\]\:animate-in {
    &[data-state="open"] {
      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[state\=open\]\:bg-accent {
    &[data-state="open"] {
      background-color: var(--accent);
    }
  }
  .data-\[state\=open\]\:bg-accent\/50 {
    &[data-state="open"] {
      background-color: var(--accent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }
  .data-\[state\=open\]\:bg-secondary {
    &[data-state="open"] {
      background-color: var(--secondary);
    }
  }
  .data-\[state\=open\]\:text-accent-foreground {
    &[data-state="open"] {
      color: var(--accent-foreground);
    }
  }
  .data-\[state\=open\]\:text-muted-foreground {
    &[data-state="open"] {
      color: var(--muted-foreground);
    }
  }
  .data-\[state\=open\]\:opacity-100 {
    &[data-state="open"] {
      opacity: 100%;
    }
  }
  .data-\[state\=open\]\:duration-500 {
    &[data-state="open"] {
      --tw-duration: 500ms;
      transition-duration: 500ms;
    }
  }
  .data-\[state\=open\]\:fade-in-0 {
    &[data-state="open"] {
      --tw-enter-opacity: calc(0/100);
      --tw-enter-opacity: 0;
    }
  }
  .data-\[state\=open\]\:zoom-in-90 {
    &[data-state="open"] {
      --tw-enter-scale: calc(90*1%);
      --tw-enter-scale: .9;
    }
  }
  .data-\[state\=open\]\:zoom-in-95 {
    &[data-state="open"] {
      --tw-enter-scale: calc(95*1%);
      --tw-enter-scale: .95;
    }
  }
  .data-\[state\=open\]\:slide-in-from-bottom {
    &[data-state="open"] {
      --tw-enter-translate-y: 100%;
    }
  }
  .data-\[state\=open\]\:slide-in-from-left {
    &[data-state="open"] {
      --tw-enter-translate-x: -100%;
    }
  }
  .data-\[state\=open\]\:slide-in-from-right {
    &[data-state="open"] {
      --tw-enter-translate-x: 100%;
    }
  }
  .data-\[state\=open\]\:slide-in-from-top {
    &[data-state="open"] {
      --tw-enter-translate-y: -100%;
    }
  }
  .data-\[state\=open\]\:slide-in-from-top-full {
    &[data-state="open"] {
      --tw-enter-translate-y: calc(1*-100%);
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="open"] {
        animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
      }
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="open"] {
        --tw-enter-opacity: calc(0/100);
        --tw-enter-opacity: 0;
      }
    }
  }
  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95 {
    &:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
      &[data-state="open"] {
        --tw-enter-scale: calc(95*1%);
        --tw-enter-scale: .95;
      }
    }
  }
  .data-\[state\=open\]\:hover\:bg-accent {
    &[data-state="open"] {
      &:hover {
        @media (hover: hover) {
          background-color: var(--accent);
        }
      }
    }
  }
  .data-\[state\=open\]\:hover\:bg-sidebar-accent {
    &[data-state="open"] {
      &:hover {
        @media (hover: hover) {
          background-color: var(--sidebar-accent);
        }
      }
    }
  }
  .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground {
    &[data-state="open"] {
      &:hover {
        @media (hover: hover) {
          color: var(--sidebar-accent-foreground);
        }
      }
    }
  }
  .data-\[state\=open\]\:focus\:bg-accent {
    &[data-state="open"] {
      &:focus {
        background-color: var(--accent);
      }
    }
  }
  .data-\[state\=selected\]\:bg-muted {
    &[data-state="selected"] {
      background-color: var(--muted);
    }
  }
  .data-\[state\=unchecked\]\:translate-x-0 {
    &[data-state="unchecked"] {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[state\=unchecked\]\:bg-input {
    &[data-state="unchecked"] {
      background-color: var(--input);
    }
  }
  .data-\[state\=visible\]\:animate-in {
    &[data-state="visible"] {
      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[state\=visible\]\:fade-in {
    &[data-state="visible"] {
      --tw-enter-opacity: 0;
    }
  }
  .data-\[swipe\=cancel\]\:translate-x-0 {
    &[data-swipe="cancel"] {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\] {
    &[data-swipe="end"] {
      --tw-translate-x: var(--radix-toast-swipe-end-x);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[swipe\=end\]\:animate-out {
    &[data-swipe="end"] {
      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,forwards);
    }
  }
  .data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\] {
    &[data-swipe="move"] {
      --tw-translate-x: var(--radix-toast-swipe-move-x);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\[swipe\=move\]\:transition-none {
    &[data-swipe="move"] {
      transition-property: none;
    }
  }
  .data-\[variant\=destructive\]\:text-destructive {
    &[data-variant="destructive"] {
      color: var(--destructive);
    }
  }
  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10 {
    &[data-variant="destructive"] {
      &:focus {
        background-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
        }
      }
    }
  }
  .data-\[variant\=destructive\]\:focus\:text-destructive {
    &[data-variant="destructive"] {
      &:focus {
        color: var(--destructive);
      }
    }
  }
  .data-\[variant\=outline\]\:border-l-0 {
    &[data-variant="outline"] {
      border-left-style: var(--tw-border-style);
      border-left-width: 0px;
    }
  }
  .data-\[variant\=outline\]\:shadow-xs {
    &[data-variant="outline"] {
      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .data-\[variant\=outline\]\:first\:border-l {
    &[data-variant="outline"] {
      &:first-child {
        border-left-style: var(--tw-border-style);
        border-left-width: 1px;
      }
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:inset-x-0 {
    &[data-vaul-drawer-direction="bottom"] {
      inset-inline: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:bottom-0 {
    &[data-vaul-drawer-direction="bottom"] {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:mt-24 {
    &[data-vaul-drawer-direction="bottom"] {
      margin-top: calc(var(--spacing) * 24);
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\] {
    &[data-vaul-drawer-direction="bottom"] {
      max-height: 80vh;
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg {
    &[data-vaul-drawer-direction="bottom"] {
      border-top-left-radius: var(--radius);
      border-top-right-radius: var(--radius);
    }
  }
  .data-\[vaul-drawer-direction\=bottom\]\:border-t {
    &[data-vaul-drawer-direction="bottom"] {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
    }
  }
  .data-\[vaul-drawer-direction\=left\]\:inset-y-0 {
    &[data-vaul-drawer-direction="left"] {
      inset-block: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=left\]\:left-0 {
    &[data-vaul-drawer-direction="left"] {
      left: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=left\]\:w-3\/4 {
    &[data-vaul-drawer-direction="left"] {
      width: calc(3/4 * 100%);
    }
  }
  .data-\[vaul-drawer-direction\=left\]\:border-r {
    &[data-vaul-drawer-direction="left"] {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .data-\[vaul-drawer-direction\=right\]\:inset-y-0 {
    &[data-vaul-drawer-direction="right"] {
      inset-block: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=right\]\:right-0 {
    &[data-vaul-drawer-direction="right"] {
      right: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=right\]\:w-3\/4 {
    &[data-vaul-drawer-direction="right"] {
      width: calc(3/4 * 100%);
    }
  }
  .data-\[vaul-drawer-direction\=right\]\:border-l {
    &[data-vaul-drawer-direction="right"] {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:inset-x-0 {
    &[data-vaul-drawer-direction="top"] {
      inset-inline: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:top-0 {
    &[data-vaul-drawer-direction="top"] {
      top: calc(var(--spacing) * 0);
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:mb-24 {
    &[data-vaul-drawer-direction="top"] {
      margin-bottom: calc(var(--spacing) * 24);
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\] {
    &[data-vaul-drawer-direction="top"] {
      max-height: 80vh;
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:rounded-b-lg {
    &[data-vaul-drawer-direction="top"] {
      border-bottom-right-radius: var(--radius);
      border-bottom-left-radius: var(--radius);
    }
  }
  .data-\[vaul-drawer-direction\=top\]\:border-b {
    &[data-vaul-drawer-direction="top"] {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .sm\:top-auto {
    @media (width >= 40rem) {
      top: auto;
    }
  }
  .sm\:right-0 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .sm\:bottom-0 {
    @media (width >= 40rem) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:max-w-lg {
    @media (width >= 40rem) {
      max-width: var(--container-lg);
    }
  }
  .sm\:max-w-sm {
    @media (width >= 40rem) {
      max-width: var(--container-sm);
    }
  }
  .sm\:flex-col {
    @media (width >= 40rem) {
      flex-direction: column;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\:gap-2\.5 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:p-12 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 12);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:pt-24 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 24);
    }
  }
  .sm\:pr-2\.5 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:pb-32 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 32);
    }
  }
  .sm\:pl-2\.5 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .sm\:text-2xl {
    @media (width >= 40rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-4xl {
    @media (width >= 40rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .sm\:text-5xl {
    @media (width >= 40rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full {
    &[data-state="open"] {
      @media (width >= 40rem) {
        --tw-enter-translate-y: calc(1*100%);
      }
    }
  }
  .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm {
    &[data-vaul-drawer-direction="left"] {
      @media (width >= 40rem) {
        max-width: var(--container-sm);
      }
    }
  }
  .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm {
    &[data-vaul-drawer-direction="right"] {
      @media (width >= 40rem) {
        max-width: var(--container-sm);
      }
    }
  }
  .md\:absolute {
    @media (width >= 48rem) {
      position: absolute;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    @media (width >= 48rem) {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:max-w-\[420px\] {
    @media (width >= 48rem) {
      max-width: 420px;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:gap-1\.5 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 1.5);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .md\:opacity-0 {
    @media (width >= 48rem) {
      opacity: 0%;
    }
  }
  .md\:peer-data-\[variant\=inset\]\:m-2 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        margin: calc(var(--spacing) * 2);
      }
    }
  }
  .md\:peer-data-\[variant\=inset\]\:ml-0 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        margin-left: calc(var(--spacing) * 0);
      }
    }
  }
  .md\:peer-data-\[variant\=inset\]\:rounded-xl {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        border-radius: calc(var(--radius) + 4px);
      }
    }
  }
  .md\:peer-data-\[variant\=inset\]\:shadow-sm {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        &:is(:where(.peer)[data-state="collapsed"] ~ *) {
          margin-left: calc(var(--spacing) * 2);
        }
      }
    }
  }
  .md\:after\:hidden {
    @media (width >= 48rem) {
      &::after {
        content: var(--tw-content);
        display: none;
      }
    }
  }
  .lg\:order-1 {
    @media (width >= 64rem) {
      order: 1;
    }
  }
  .lg\:order-2 {
    @media (width >= 64rem) {
      order: 2;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:text-6xl {
    @media (width >= 64rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .dark\:border-input {
    &:is(.dark *) {
      border-color: var(--input);
    }
  }
  .dark\:bg-destructive\/60 {
    &:is(.dark *) {
      background-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
  }
  .dark\:bg-input\/30 {
    &:is(.dark *) {
      background-color: var(--input);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--input) 30%, transparent);
      }
    }
  }
  .dark\:text-muted-foreground {
    &:is(.dark *) {
      color: var(--muted-foreground);
    }
  }
  .dark\:hover\:bg-accent\/50 {
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--accent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--accent) 50%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:bg-input\/50 {
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--input);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--input) 50%, transparent);
          }
        }
      }
    }
  }
  .dark\:hover\:text-accent-foreground {
    &:is(.dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--accent-foreground);
        }
      }
    }
  }
  .dark\:focus-visible\:ring-destructive\/40 {
    &:is(.dark *) {
      &:focus-visible {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
      }
    }
  }
  .dark\:aria-invalid\:ring-destructive\/40 {
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
        }
      }
    }
  }
  .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40 {
    &:is(.dark *) {
      &[data-active="true"] {
        &[aria-invalid="true"] {
          --tw-ring-color: var(--destructive);
          @supports (color: color-mix(in lab, red, red)) {
            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
          }
        }
      }
    }
  }
  .dark\:data-\[state\=active\]\:border-input {
    &:is(.dark *) {
      &[data-state="active"] {
        border-color: var(--input);
      }
    }
  }
  .dark\:data-\[state\=active\]\:bg-input\/30 {
    &:is(.dark *) {
      &[data-state="active"] {
        background-color: var(--input);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--input) 30%, transparent);
        }
      }
    }
  }
  .dark\:data-\[state\=active\]\:text-foreground {
    &:is(.dark *) {
      &[data-state="active"] {
        color: var(--foreground);
      }
    }
  }
  .dark\:data-\[state\=checked\]\:bg-primary {
    &:is(.dark *) {
      &[data-state="checked"] {
        background-color: var(--primary);
      }
    }
  }
  .dark\:data-\[state\=checked\]\:bg-primary-foreground {
    &:is(.dark *) {
      &[data-state="checked"] {
        background-color: var(--primary-foreground);
      }
    }
  }
  .dark\:data-\[state\=unchecked\]\:bg-foreground {
    &:is(.dark *) {
      &[data-state="unchecked"] {
        background-color: var(--foreground);
      }
    }
  }
  .dark\:data-\[state\=unchecked\]\:bg-input\/80 {
    &:is(.dark *) {
      &[data-state="unchecked"] {
        background-color: var(--input);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--input) 80%, transparent);
        }
      }
    }
  }
  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20 {
    &:is(.dark *) {
      &[data-variant="destructive"] {
        &:focus {
          background-color: var(--destructive);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
          }
        }
      }
    }
  }
  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground {
    & .recharts-cartesian-axis-tick text {
      fill: var(--muted-foreground);
    }
  }
  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 {
    & .recharts-cartesian-grid line[stroke='#ccc'] {
      stroke: var(--border);
      @supports (color: color-mix(in lab, red, red)) {
        stroke: color-mix(in oklab, var(--border) 50%, transparent);
      }
    }
  }
  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border {
    & .recharts-curve.recharts-tooltip-cursor {
      stroke: var(--border);
    }
  }
  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent {
    & .recharts-dot[stroke='#fff'] {
      stroke: transparent;
    }
  }
  .\[\&_\.recharts-layer\]\:outline-hidden {
    & .recharts-layer {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border {
    & .recharts-polar-grid [stroke='#ccc'] {
      stroke: var(--border);
    }
  }
  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted {
    & .recharts-radial-bar-background-sector {
      fill: var(--muted);
    }
  }
  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted {
    & .recharts-rectangle.recharts-tooltip-cursor {
      fill: var(--muted);
    }
  }
  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border {
    & .recharts-reference-line [stroke='#ccc'] {
      stroke: var(--border);
    }
  }
  .\[\&_\.recharts-sector\]\:outline-hidden {
    & .recharts-sector {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent {
    & .recharts-sector[stroke='#fff'] {
      stroke: transparent;
    }
  }
  .\[\&_\.recharts-surface\]\:outline-hidden {
    & .recharts-surface {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .\[\&_\[cmdk-group-heading\]\]\:px-2 {
    & [cmdk-group-heading] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 {
    & [cmdk-group-heading] {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }
  .\[\&_\[cmdk-group-heading\]\]\:text-xs {
    & [cmdk-group-heading] {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .\[\&_\[cmdk-group-heading\]\]\:font-medium {
    & [cmdk-group-heading] {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground {
    & [cmdk-group-heading] {
      color: var(--muted-foreground);
    }
  }
  .\[\&_\[cmdk-group\]\]\:px-2 {
    & [cmdk-group] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 {
    & [cmdk-group]:not([hidden]) ~[cmdk-group] {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 {
    & [cmdk-input-wrapper] svg {
      height: calc(var(--spacing) * 5);
    }
  }
  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 {
    & [cmdk-input-wrapper] svg {
      width: calc(var(--spacing) * 5);
    }
  }
  .\[\&_\[cmdk-input\]\]\:h-12 {
    & [cmdk-input] {
      height: calc(var(--spacing) * 12);
    }
  }
  .\[\&_\[cmdk-item\]\]\:px-2 {
    & [cmdk-item] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\[\&_\[cmdk-item\]\]\:py-3 {
    & [cmdk-item] {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .\[\&_\[cmdk-item\]_svg\]\:h-5 {
    & [cmdk-item] svg {
      height: calc(var(--spacing) * 5);
    }
  }
  .\[\&_\[cmdk-item\]_svg\]\:w-5 {
    & [cmdk-item] svg {
      width: calc(var(--spacing) * 5);
    }
  }
  .\[\&_p\]\:leading-relaxed {
    & p {
      --tw-leading: var(--leading-relaxed);
      line-height: var(--leading-relaxed);
    }
  }
  .\[\&_svg\]\:pointer-events-none {
    & svg {
      pointer-events: none;
    }
  }
  .\[\&_svg\]\:shrink-0 {
    & svg {
      flex-shrink: 0;
    }
  }
  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 {
    & svg:not([class*='size-']) {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground {
    & svg:not([class*='text-']) {
      color: var(--muted-foreground);
    }
  }
  .\[\&_tr\]\:border-b {
    & tr {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .\[\&_tr\:last-child\]\:border-0 {
    & tr:last-child {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .\[\&\:first-child\[data-selected\=true\]_button\]\:rounded-l-md {
    &:first-child[data-selected=true] button {
      border-top-left-radius: calc(var(--radius) - 2px);
      border-bottom-left-radius: calc(var(--radius) - 2px);
    }
  }
  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0 {
    &:has([role=checkbox]) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .\[\.border-b\]\:pb-6 {
    &:is(.border-b) {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }
  .\[\.border-t\]\:pt-6 {
    &:is(.border-t) {
      padding-top: calc(var(--spacing) * 6);
    }
  }
  .rtl\:\*\*\:\[\.rdp-button\\_next\>svg\]\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      :is(& *) {
        &:is(.rdp-button_next>svg) {
          rotate: 180deg;
        }
      }
    }
  }
  .rtl\:\*\*\:\[\.rdp-button\\_previous\>svg\]\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      :is(& *) {
        &:is(.rdp-button_previous>svg) {
          rotate: 180deg;
        }
      }
    }
  }
  .\*\:\[span\]\:last\:flex {
    :is(& > *) {
      &:is(span) {
        &:last-child {
          display: flex;
        }
      }
    }
  }
  .\*\:\[span\]\:last\:items-center {
    :is(& > *) {
      &:is(span) {
        &:last-child {
          align-items: center;
        }
      }
    }
  }
  .\*\:\[span\]\:last\:gap-2 {
    :is(& > *) {
      &:is(span) {
        &:last-child {
          gap: calc(var(--spacing) * 2);
        }
      }
    }
  }
  .data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive {
    &[data-variant="destructive"] {
      :is(& > *) {
        &:is(svg) {
          color: var(--destructive) !important;
        }
      }
    }
  }
  .\[\&\:last-child\[data-selected\=true\]_button\]\:rounded-r-md {
    &:last-child[data-selected=true] button {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] {
    &>[role=checkbox] {
      --tw-translate-y: 2px;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .\[\&\>button\]\:hidden {
    &>button {
      display: none;
    }
  }
  .\[\&\>span\]\:text-xs {
    &>span {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .\[\&\>span\]\:opacity-70 {
    &>span {
      opacity: 70%;
    }
  }
  .\[\&\>span\:last-child\]\:truncate {
    &>span:last-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .\[\&\>svg\]\:pointer-events-none {
    &>svg {
      pointer-events: none;
    }
  }
  .\[\&\>svg\]\:size-3 {
    &>svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
  .\[\&\>svg\]\:size-3\.5 {
    &>svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .\[\&\>svg\]\:size-4 {
    &>svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\[\&\>svg\]\:h-2\.5 {
    &>svg {
      height: calc(var(--spacing) * 2.5);
    }
  }
  .\[\&\>svg\]\:h-3 {
    &>svg {
      height: calc(var(--spacing) * 3);
    }
  }
  .\[\&\>svg\]\:w-2\.5 {
    &>svg {
      width: calc(var(--spacing) * 2.5);
    }
  }
  .\[\&\>svg\]\:w-3 {
    &>svg {
      width: calc(var(--spacing) * 3);
    }
  }
  .\[\&\>svg\]\:shrink-0 {
    &>svg {
      flex-shrink: 0;
    }
  }
  .\[\&\>svg\]\:translate-y-0\.5 {
    &>svg {
      --tw-translate-y: calc(var(--spacing) * 0.5);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .\[\&\>svg\]\:text-current {
    &>svg {
      color: currentcolor;
    }
  }
  .\[\&\>svg\]\:text-muted-foreground {
    &>svg {
      color: var(--muted-foreground);
    }
  }
  .\[\&\>svg\]\:text-sidebar-accent-foreground {
    &>svg {
      color: var(--sidebar-accent-foreground);
    }
  }
  .\[\&\>tr\]\:last\:border-b-0 {
    &>tr {
      &:last-child {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 0px;
      }
    }
  }
  .\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90 {
    &[data-panel-group-direction=vertical]>div {
      rotate: 90deg;
    }
  }
  .\[\&\[data-state\=open\]\>svg\]\:rotate-180 {
    &[data-state=open]>svg {
      rotate: 180deg;
    }
  }
  .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    [data-side=left][data-collapsible=offcanvas] & {
      right: calc(var(--spacing) * -2);
    }
  }
  .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    [data-side=left][data-state=collapsed] & {
      cursor: e-resize;
    }
  }
  .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    [data-side=right][data-collapsible=offcanvas] & {
      left: calc(var(--spacing) * -2);
    }
  }
  .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    [data-side=right][data-state=collapsed] & {
      cursor: w-resize;
    }
  }
  .\[\[data-slot\=card-content\]_\&\]\:bg-transparent {
    [data-slot=card-content] & {
      background-color: transparent;
    }
  }
  .\[\[data-slot\=popover-content\]_\&\]\:bg-transparent {
    [data-slot=popover-content] & {
      background-color: transparent;
    }
  }
  .\[a\&\]\:hover\:bg-accent {
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--accent);
        }
      }
    }
  }
  .\[a\&\]\:hover\:bg-destructive\/90 {
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--destructive);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
          }
        }
      }
    }
  }
  .\[a\&\]\:hover\:bg-primary\/90 {
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--primary);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--primary) 90%, transparent);
          }
        }
      }
    }
  }
  .\[a\&\]\:hover\:bg-secondary\/90 {
    a& {
      &:hover {
        @media (hover: hover) {
          background-color: var(--secondary);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
          }
        }
      }
    }
  }
  .\[a\&\]\:hover\:text-accent-foreground {
    a& {
      &:hover {
        @media (hover: hover) {
          color: var(--accent-foreground);
        }
      }
    }
  }
}
@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}
@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}
@property --tw-animation-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: forwards;
}
@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}
@layer base {
  * {
    border-color: var(--border);
    outline-color: var(--ring);
    @supports (color: color-mix(in lab, red, red)) {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity,1);
    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));
  }
}
@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity,1);
    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));
  }
}
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));
  }
}
@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))));
  }
  to {
    height: 0;
  }
}
@keyframes caret-blink {
  0%,70%,100% {
    opacity: 1;
  }
  20%,50% {
    opacity: 0;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: forwards;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

